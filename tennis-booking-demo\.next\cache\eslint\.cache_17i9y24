[{"C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\app\\about\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\app\\contact\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\app\\demo\\page.tsx": "3", "C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\app\\layout.tsx": "4", "C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\app\\login\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\app\\page.tsx": "6", "C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\app\\privacy\\page.tsx": "7", "C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\app\\signup\\page.tsx": "8", "C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\app\\terms\\page.tsx": "9", "C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\components\\landing\\Features.tsx": "10", "C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\components\\landing\\Hero.tsx": "11", "C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\components\\landing\\Pricing.tsx": "12", "C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\components\\landing\\ProblemSection.tsx": "13", "C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\components\\layout\\Footer.tsx": "14", "C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\components\\layout\\Navigation.tsx": "15", "C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\components\\ui\\button.tsx": "16", "C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\components\\ui\\card.tsx": "17", "C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\lib\\translations.ts": "18", "C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\lib\\utils.ts": "19"}, {"size": 3593, "mtime": 1752178301422, "results": "20", "hashOfConfig": "21"}, {"size": 8799, "mtime": 1752174891933, "results": "22", "hashOfConfig": "21"}, {"size": 6796, "mtime": 1752173575186, "results": "23", "hashOfConfig": "21"}, {"size": 1157, "mtime": 1752090020939, "results": "24", "hashOfConfig": "21"}, {"size": 7386, "mtime": 1752173472713, "results": "25", "hashOfConfig": "21"}, {"size": 908, "mtime": 1752173355039, "results": "26", "hashOfConfig": "21"}, {"size": 3248, "mtime": 1752174940515, "results": "27", "hashOfConfig": "21"}, {"size": 8428, "mtime": 1752173521055, "results": "28", "hashOfConfig": "21"}, {"size": 3294, "mtime": 1752174924411, "results": "29", "hashOfConfig": "21"}, {"size": 5287, "mtime": 1752176683512, "results": "30", "hashOfConfig": "21"}, {"size": 7547, "mtime": 1752178288485, "results": "31", "hashOfConfig": "21"}, {"size": 5771, "mtime": 1752173227584, "results": "32", "hashOfConfig": "21"}, {"size": 7573, "mtime": 1752090178961, "results": "33", "hashOfConfig": "21"}, {"size": 6992, "mtime": 1752173265228, "results": "34", "hashOfConfig": "21"}, {"size": 4085, "mtime": 1752178277932, "results": "35", "hashOfConfig": "21"}, {"size": 1709, "mtime": 1752074896409, "results": "36", "hashOfConfig": "21"}, {"size": 1875, "mtime": 1752074909507, "results": "37", "hashOfConfig": "21"}, {"size": 22054, "mtime": 1752176435025, "results": "38", "hashOfConfig": "21"}, {"size": 166, "mtime": 1752074604382, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "19zvrqf", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\app\\about\\page.tsx", ["97", "98"], [], "C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\app\\contact\\page.tsx", ["99"], [], "C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\app\\demo\\page.tsx", ["100"], [], "C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\app\\login\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\app\\privacy\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\app\\signup\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\app\\terms\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\components\\landing\\Features.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\components\\landing\\Hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\components\\landing\\Pricing.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\components\\landing\\ProblemSection.tsx", ["101", "102", "103"], [], "C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\components\\layout\\Footer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\components\\layout\\Navigation.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\components\\ui\\button.tsx", ["104"], [], "C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\lib\\translations.ts", [], [], "C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\lib\\utils.ts", [], [], {"ruleId": "105", "severity": 2, "message": "106", "line": 25, "column": 15, "nodeType": "107", "messageId": "108", "suggestions": "109"}, {"ruleId": "105", "severity": 2, "message": "106", "line": 37, "column": 69, "nodeType": "107", "messageId": "108", "suggestions": "110"}, {"ruleId": "105", "severity": 2, "message": "106", "line": 43, "column": 56, "nodeType": "107", "messageId": "108", "suggestions": "111"}, {"ruleId": "105", "severity": 2, "message": "106", "line": 82, "column": 90, "nodeType": "107", "messageId": "108", "suggestions": "112"}, {"ruleId": "105", "severity": 2, "message": "113", "line": 129, "column": 19, "nodeType": "107", "messageId": "108", "suggestions": "114"}, {"ruleId": "105", "severity": 2, "message": "113", "line": 129, "column": 76, "nodeType": "107", "messageId": "108", "suggestions": "115"}, {"ruleId": "105", "severity": 2, "message": "106", "line": 179, "column": 18, "nodeType": "107", "messageId": "108", "suggestions": "116"}, {"ruleId": "117", "severity": 2, "message": "118", "line": 38, "column": 32, "nodeType": null, "messageId": "119", "endLine": 38, "endColumn": 39}, "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["120", "121", "122", "123"], ["124", "125", "126", "127"], ["128", "129", "130", "131"], ["132", "133", "134", "135"], "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["136", "137", "138", "139"], ["140", "141", "142", "143"], ["144", "145", "146", "147"], "@typescript-eslint/no-unused-vars", "'as<PERSON><PERSON>d' is assigned a value but never used.", "unusedVar", {"messageId": "148", "data": "149", "fix": "150", "desc": "151"}, {"messageId": "148", "data": "152", "fix": "153", "desc": "154"}, {"messageId": "148", "data": "155", "fix": "156", "desc": "157"}, {"messageId": "148", "data": "158", "fix": "159", "desc": "160"}, {"messageId": "148", "data": "161", "fix": "162", "desc": "151"}, {"messageId": "148", "data": "163", "fix": "164", "desc": "154"}, {"messageId": "148", "data": "165", "fix": "166", "desc": "157"}, {"messageId": "148", "data": "167", "fix": "168", "desc": "160"}, {"messageId": "148", "data": "169", "fix": "170", "desc": "151"}, {"messageId": "148", "data": "171", "fix": "172", "desc": "154"}, {"messageId": "148", "data": "173", "fix": "174", "desc": "157"}, {"messageId": "148", "data": "175", "fix": "176", "desc": "160"}, {"messageId": "148", "data": "177", "fix": "178", "desc": "151"}, {"messageId": "148", "data": "179", "fix": "180", "desc": "154"}, {"messageId": "148", "data": "181", "fix": "182", "desc": "157"}, {"messageId": "148", "data": "183", "fix": "184", "desc": "160"}, {"messageId": "148", "data": "185", "fix": "186", "desc": "187"}, {"messageId": "148", "data": "188", "fix": "189", "desc": "190"}, {"messageId": "148", "data": "191", "fix": "192", "desc": "193"}, {"messageId": "148", "data": "194", "fix": "195", "desc": "196"}, {"messageId": "148", "data": "197", "fix": "198", "desc": "187"}, {"messageId": "148", "data": "199", "fix": "200", "desc": "190"}, {"messageId": "148", "data": "201", "fix": "202", "desc": "193"}, {"messageId": "148", "data": "203", "fix": "204", "desc": "196"}, {"messageId": "148", "data": "205", "fix": "206", "desc": "151"}, {"messageId": "148", "data": "207", "fix": "208", "desc": "154"}, {"messageId": "148", "data": "209", "fix": "210", "desc": "157"}, {"messageId": "148", "data": "211", "fix": "212", "desc": "160"}, "replaceWithAlt", {"alt": "213"}, {"range": "214", "text": "215"}, "Replace with `&apos;`.", {"alt": "216"}, {"range": "217", "text": "218"}, "Replace with `&lsquo;`.", {"alt": "219"}, {"range": "220", "text": "221"}, "Replace with `&#39;`.", {"alt": "222"}, {"range": "223", "text": "224"}, "Replace with `&rsquo;`.", {"alt": "213"}, {"range": "225", "text": "226"}, {"alt": "216"}, {"range": "227", "text": "228"}, {"alt": "219"}, {"range": "229", "text": "230"}, {"alt": "222"}, {"range": "231", "text": "232"}, {"alt": "213"}, {"range": "233", "text": "234"}, {"alt": "216"}, {"range": "235", "text": "236"}, {"alt": "219"}, {"range": "237", "text": "238"}, {"alt": "222"}, {"range": "239", "text": "240"}, {"alt": "213"}, {"range": "241", "text": "242"}, {"alt": "216"}, {"range": "243", "text": "244"}, {"alt": "219"}, {"range": "245", "text": "246"}, {"alt": "222"}, {"range": "247", "text": "248"}, {"alt": "249"}, {"range": "250", "text": "251"}, "Replace with `&quot;`.", {"alt": "252"}, {"range": "253", "text": "254"}, "Replace with `&ldquo;`.", {"alt": "255"}, {"range": "256", "text": "257"}, "Replace with `&#34;`.", {"alt": "258"}, {"range": "259", "text": "260"}, "Replace with `&rdquo;`.", {"alt": "249"}, {"range": "261", "text": "262"}, {"alt": "252"}, {"range": "263", "text": "264"}, {"alt": "255"}, {"range": "265", "text": "266"}, {"alt": "258"}, {"range": "267", "text": "268"}, {"alt": "213"}, {"range": "269", "text": "270"}, {"alt": "216"}, {"range": "271", "text": "272"}, {"alt": "219"}, {"range": "273", "text": "274"}, {"alt": "222"}, {"range": "275", "text": "276"}, "&apos;", [1073, 1291], "\n            We&apos;re on a mission to modernize tennis club management worldwide. \n            Our platform helps clubs save time, increase revenue, and create \n            better experiences for their members.\n          ", "&lsquo;", [1073, 1291], "\n            We&lsquo;re on a mission to modernize tennis club management worldwide. \n            Our platform helps clubs save time, increase revenue, and create \n            better experiences for their members.\n          ", "&#39;", [1073, 1291], "\n            We&#39;re on a mission to modernize tennis club management worldwide. \n            Our platform helps clubs save time, increase revenue, and create \n            better experiences for their members.\n          ", "&rsquo;", [1073, 1291], "\n            We&rsquo;re on a mission to modernize tennis club management worldwide. \n            Our platform helps clubs save time, increase revenue, and create \n            better experiences for their members.\n          ", [1723, 1742], "Why We&apos;re Different", [1723, 1742], "Why We&lsquo;re Different", [1723, 1742], "Why We&#39;re Different", [1723, 1742], "Why We&rsquo;re Different", [1491, 1630], "\n            Have questions about Tennis Booking Pro? We&apos;re here to help you transform \n            your tennis club management.\n          ", [1491, 1630], "\n            Have questions about Tennis Booking Pro? We&lsquo;re here to help you transform \n            your tennis club management.\n          ", [1491, 1630], "\n            Have questions about Tennis Booking Pro? We&#39;re here to help you transform \n            your tennis club management.\n          ", [1491, 1630], "\n            Have questions about Tennis Booking Pro? We&rsquo;re here to help you transform \n            your tennis club management.\n          ", [4014, 4128], "\n              Explore the comprehensive analytics dashboard that helps optimize your club&apos;s revenue.\n            ", [4014, 4128], "\n              Explore the comprehensive analytics dashboard that helps optimize your club&lsquo;s revenue.\n            ", [4014, 4128], "\n              Explore the comprehensive analytics dashboard that helps optimize your club&#39;s revenue.\n            ", [4014, 4128], "\n              Explore the comprehensive analytics dashboard that helps optimize your club&rsquo;s revenue.\n            ", "&quot;", [5355, 5449], "\n                  &quot;We used to spend 4 hours daily just managing bookings...\"\n                ", "&ldquo;", [5355, 5449], "\n                  &ldquo;We used to spend 4 hours daily just managing bookings...\"\n                ", "&#34;", [5355, 5449], "\n                  &#34;We used to spend 4 hours daily just managing bookings...\"\n                ", "&rdquo;", [5355, 5449], "\n                  &rdquo;We used to spend 4 hours daily just managing bookings...\"\n                ", [5355, 5449], "\n                  \"We used to spend 4 hours daily just managing bookings...&quot;\n                ", [5355, 5449], "\n                  \"We used to spend 4 hours daily just managing bookings...&ldquo;\n                ", [5355, 5449], "\n                  \"We used to spend 4 hours daily just managing bookings...&#34;\n                ", [5355, 5449], "\n                  \"We used to spend 4 hours daily just managing bookings...&rdquo;\n                ", [7450, 7496], "\n            There&apos;s a better way ↓\n          ", [7450, 7496], "\n            There&lsquo;s a better way ↓\n          ", [7450, 7496], "\n            There&#39;s a better way ↓\n          ", [7450, 7496], "\n            There&rsquo;s a better way ↓\n          "]