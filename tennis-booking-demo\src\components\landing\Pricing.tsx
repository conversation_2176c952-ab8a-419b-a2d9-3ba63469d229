'use client'

import Link from 'next/link'

interface PricingProps {
  translations: {
    title: string
    subtitle: string
    plans: {
      name: string
      price: string
      description: string
      features: string[]
      cta: string
      popular?: boolean
    }[]
  }
}

export function Pricing({ translations }: PricingProps) {
  return (
    <section id="pricing" className="section-padding bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="heading-lg mb-6">
            {translations.title}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {translations.subtitle}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
          {translations.plans.map((plan, index) => (
            <div
              key={index}
              className={`pricing-card relative ${plan.popular ? 'featured' : ''}`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <span className="bg-blue-600 text-white px-4 py-1 rounded-full text-sm font-semibold">
                    Most Popular
                  </span>
                </div>
              )}

              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-2">
                  {plan.name}
                </h3>
                
                <div className="mb-4">
                  <span className="text-4xl font-bold text-gray-900">
                    {plan.price}
                  </span>
                  {plan.price !== 'Custom' && (
                    <span className="text-gray-500 ml-1">/month</span>
                  )}
                </div>
                
                <p className="text-gray-600">
                  {plan.description}
                </p>
              </div>

              <ul className="space-y-4 mb-8">
                {plan.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-start">
                    <svg 
                      className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" 
                      fill="none" 
                      stroke="currentColor" 
                      viewBox="0 0 24 24"
                    >
                      <path 
                        strokeLinecap="round" 
                        strokeLinejoin="round" 
                        strokeWidth={2} 
                        d="M5 13l4 4L19 7" 
                      />
                    </svg>
                    <span className="text-gray-700">{feature}</span>
                  </li>
                ))}
              </ul>

              <Link
                href="/signup"
                className={`w-full text-center block py-3 px-6 rounded-lg font-semibold transition-colors ${
                  plan.popular
                    ? 'bg-blue-600 text-white hover:bg-blue-700'
                    : 'bg-gray-100 text-gray-900 hover:bg-gray-200'
                }`}
              >
                {plan.cta}
              </Link>
            </div>
          ))}
        </div>

        {/* Price Comparison */}
        <div className="mt-16 text-center">
          <div className="bg-orange-50 border border-orange-200 rounded-xl p-6 max-w-2xl mx-auto">
            <h3 className="text-lg font-semibold text-orange-900 mb-2">
              💰 Save Money Compared to Competitors
            </h3>
            <p className="text-orange-800">
              <span className="line-through text-orange-600">CourtReserve: €99/month</span>
              {' → '}
              <span className="font-bold text-orange-900">Tennis Booking Pro: €39/month</span>
              {' '}
              <span className="bg-orange-200 text-orange-900 px-2 py-1 rounded text-sm font-semibold">
                Save 61%
              </span>
            </p>
          </div>
        </div>

        {/* FAQ */}
        <div className="mt-16">
          <h3 className="text-2xl font-bold text-center text-gray-900 mb-8">
            Frequently Asked Questions
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">
                How long does setup take?
              </h4>
              <p className="text-gray-600">
                Most clubs are live within 24 hours. We provide free onboarding and training.
              </p>
            </div>
            
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">
                Can we keep our existing website?
              </h4>
              <p className="text-gray-600">
                Yes! We integrate seamlessly with any website or provide a standalone solution.
              </p>
            </div>
            
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">
                Is member data secure?
              </h4>
              <p className="text-gray-600">
                Absolutely. GDPR compliant with enterprise-grade security and daily backups.
              </p>
            </div>
            
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">
                What if we outgrow the free plan?
              </h4>
              <p className="text-gray-600">
                Upgrade anytime with no data loss. Start free and scale as you grow.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}