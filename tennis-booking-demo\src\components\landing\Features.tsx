'use client'

interface FeaturesProps {
  translations: {
    title: string
    subtitle: string
    features: {
      title: string
      description: string
    }[]
  }
}

const icons = [
  // Lightning for instant booking
  <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" key="lightning">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
  </svg>,
  // Users for member management
  <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" key="users">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
  </svg>,
  // Chart for analytics
  <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" key="chart">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
  </svg>,
  // Mobile for mobile-first
  <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" key="mobile">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a1 1 0 001-1V4a1 1 0 00-1-1H8a1 1 0 00-1 1v16a1 1 0 001 1z" />
  </svg>,
  // Credit card for payments
  <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" key="payment">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
  </svg>,
  // Globe for multi-language
  <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" key="globe">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
  </svg>
]

export function Features({ translations }: FeaturesProps) {
  return (
    <section id="features" className="section-padding bg-gradient-to-b from-white to-gray-50 relative overflow-hidden">
      {/* Premium Background Effects */}
      <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-br from-blue-100/20 to-purple-100/20 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-64 h-64 bg-gradient-to-br from-purple-100/20 to-pink-100/20 rounded-full blur-3xl"></div>
      
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-20 animate-in fade-in duration-1000">
          <h2 className="heading-lg mb-6 text-gradient">
            {translations.title}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {translations.subtitle}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {translations.features.map((feature, index) => (
            <div 
              key={index} 
              className="feature-card text-center group animate-in fade-in duration-1000"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <div className="w-20 h-20 bg-gradient-to-br from-blue-100 to-purple-50 rounded-3xl flex items-center justify-center text-blue-600 mx-auto mb-8 group-hover:scale-110 transition-all duration-300 shadow-lg group-hover:shadow-xl">
                {icons[index % icons.length]}
              </div>
              
              <h3 className="heading-md mb-6 text-gradient">
                {feature.title}
              </h3>
              
              <p className="text-gray-600 leading-relaxed">
                {feature.description}
              </p>
            </div>
          ))}
        </div>

        {/* Premium Trust Bar */}
        <div className="mt-20 pt-16 relative">
          <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div>
          
          <div className="text-center mb-12 animate-in fade-in duration-1000 delay-800">
            <p className="text-gray-500 font-medium text-lg">Trusted by leading tennis clubs worldwide</p>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 items-center">
            {[
              { name: "Tennis Club Berlin", delay: "900ms" },
              { name: "Vienna Courts", delay: "1000ms" },
              { name: "Amsterdam Tennis", delay: "1100ms" },
              { name: "Madrid Club", delay: "1200ms" }
            ].map((club, index) => (
              <div 
                key={index}
                className="glass-card h-16 flex items-center justify-center hover:transform hover:scale-105 transition-all duration-300 animate-in fade-in duration-1000"
                style={{ animationDelay: club.delay }}
              >
                <span className="text-gray-600 font-semibold text-sm">{club.name}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}