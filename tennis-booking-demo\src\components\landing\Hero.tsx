'use client'

import Link from 'next/link'
import Image from 'next/image'

interface HeroProps {
  translations: {
    headline: string
    subheadline: string
    startTrial: string
    watchDemo: string
    stat1: string
    stat2: string
    stat3: string
    stat4: string
  }
}

export function Hero({ translations }: HeroProps) {
  return (
    <section className="relative bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/20 section-padding overflow-hidden">
      {/* Premium Background Effects */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,_var(--tw-gradient-stops))] from-blue-100/20 via-transparent to-purple-100/20"></div>
      <div className="absolute top-0 left-1/3 w-96 h-96 bg-gradient-to-br from-blue-400/10 to-purple-400/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 right-1/4 w-64 h-64 bg-gradient-to-br from-purple-400/10 to-pink-400/10 rounded-full blur-3xl"></div>
      
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* Left Column - Content */}
          <div className="text-center lg:text-left space-y-8">
            <div className="animate-in fade-in duration-1000">
              <h1 className="heading-xl mb-6 animate-in slide-in-from-left duration-1000">
                {translations.headline}
              </h1>
            </div>
            
            <div className="animate-in fade-in duration-1000 delay-200">
              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                {translations.subheadline}
              </p>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 mb-12 justify-center lg:justify-start animate-in fade-in duration-1000 delay-400">
              <Link href="/signup" className="btn-primary text-lg px-8 py-4 hover:transform hover:scale-105 transition-all duration-300">
                {translations.startTrial}
              </Link>
              <Link href="/demo" className="btn-secondary text-lg px-8 py-4 hover:transform hover:scale-105 transition-all duration-300">
                {translations.watchDemo}
              </Link>
            </div>

            {/* Premium Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 animate-in fade-in duration-1000 delay-600">
              <div className="text-center group hover:transform hover:scale-110 transition-all duration-300">
                <div className="glass-card p-4 rounded-xl">
                  <div className="text-3xl font-bold text-gradient mb-1">2 sec</div>
                  <div className="text-sm text-gray-600">{translations.stat1}</div>
                </div>
              </div>
              <div className="text-center group hover:transform hover:scale-110 transition-all duration-300">
                <div className="glass-card p-4 rounded-xl">
                  <div className="text-3xl font-bold text-gradient mb-1">6</div>
                  <div className="text-sm text-gray-600">{translations.stat2}</div>
                </div>
              </div>
              <div className="text-center group hover:transform hover:scale-110 transition-all duration-300">
                <div className="glass-card p-4 rounded-xl">
                  <div className="text-3xl font-bold text-gradient mb-1">50%</div>
                  <div className="text-sm text-gray-600">{translations.stat3}</div>
                </div>
              </div>
              <div className="text-center group hover:transform hover:scale-110 transition-all duration-300">
                <div className="glass-card p-4 rounded-xl">
                  <div className="text-3xl font-bold text-gradient mb-1">500+</div>
                  <div className="text-sm text-gray-600">{translations.stat4}</div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Premium Image */}
          <div className="relative animate-in fade-in duration-1000 delay-300">
            <div className="relative rounded-3xl overflow-hidden shadow-2xl hover:shadow-premium transition-all duration-500 group">
              <Image
                src="/clubbuilding.jpg"
                alt="Modern tennis club"
                width={600}
                height={400}
                className="w-full h-auto object-cover"
                priority
              />
              
              {/* Premium Trust Badge */}
              <div className="absolute top-6 left-6 glass-card px-4 py-3 animate-in slide-in-from-left duration-1000 delay-800">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full animate-pulse"></div>
                  <span className="text-sm font-semibold text-gray-900">
                    500+ Clubs Trust Us
                  </span>
                </div>
              </div>

              {/* Premium Bottom Badge */}
              <div className="absolute bottom-6 right-6 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl px-4 py-3 shadow-lg animate-in slide-in-from-right duration-1000 delay-1000">
                <div className="text-sm font-semibold">
                  50% Cheaper Than Competitors
                </div>
              </div>
            </div>

            {/* Premium Floating Cards */}
            <div className="absolute -top-8 -right-8 glass-card p-5 hover:transform hover:scale-105 transition-all duration-300 animate-in slide-in-from-right duration-1000 delay-1200">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-gradient-to-br from-green-100 to-emerald-50 rounded-xl flex items-center justify-center shadow-md">
                  <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <div>
                  <div className="text-sm font-semibold text-gray-900">Instant Booking</div>
                  <div className="text-xs text-gray-500">2 seconds average</div>
                </div>
              </div>
            </div>

            <div className="absolute -bottom-8 -left-8 glass-card p-5 hover:transform hover:scale-105 transition-all duration-300 animate-in slide-in-from-left duration-1000 delay-1400">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-100 to-blue-50 rounded-xl flex items-center justify-center shadow-md">
                  <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                  </svg>
                </div>
                <div>
                  <div className="text-sm font-semibold text-gray-900">Save Money</div>
                  <div className="text-xs text-gray-500">€39/month only</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}