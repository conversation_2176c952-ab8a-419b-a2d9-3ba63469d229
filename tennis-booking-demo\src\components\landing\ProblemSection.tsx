'use client'

import { motion } from 'framer-motion'
import { useInView } from 'framer-motion'
import { useRef } from 'react'
import Image from 'next/image'

const problems = [
  {
    icon: '📞',
    title: 'Phone Booking Hell',
    description: 'Members calling at all hours, double bookings, lost reservations, overwhelmed staff',
    color: 'from-red-500 to-pink-500'
  },
  {
    icon: '💸',
    title: 'Revenue Leaks',
    description: 'No-shows, manual tracking, missed peak opportunities, complex pricing setup',
    color: 'from-orange-500 to-red-500'
  },
  {
    icon: '😤',
    title: 'Member Frustration',
    description: 'Outdated systems driving members to competitors, poor mobile experience',
    color: 'from-purple-500 to-pink-500'
  }
]

export function ProblemSection() {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })

  return (
    <section ref={ref} className="py-20 bg-gradient-to-b from-slate-50 to-white relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute top-10 left-10 w-20 h-20 bg-red-200 rounded-full blur-xl" />
        <div className="absolute top-40 right-20 w-32 h-32 bg-orange-200 rounded-full blur-2xl" />
        <div className="absolute bottom-20 left-1/3 w-24 h-24 bg-purple-200 rounded-full blur-xl" />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8 }}
        >
          <motion.h2
            className="text-3xl md:text-5xl font-bold text-slate-900 mb-6"
            initial={{ opacity: 0, y: 30 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 0.8, delay: 0.1 }}
          >
            Still Managing Your Club{' '}
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-red-500 to-orange-500">
              the Old Way?
            </span>
          </motion.h2>
          
          <motion.p
            className="text-xl text-slate-600 max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 30 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            These outdated management headaches are costing you members and revenue every day
          </motion.p>
        </motion.div>

        {/* Problems Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          {problems.map((problem, index) => (
            <motion.div
              key={problem.title}
              className="relative group"
              initial={{ opacity: 0, y: 50 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
              transition={{ duration: 0.8, delay: 0.3 + index * 0.1 }}
            >
              <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 border border-slate-100 group-hover:border-slate-200 h-full">
                {/* Icon with Gradient Background */}
                <div className={`w-16 h-16 rounded-full bg-gradient-to-r ${problem.color} flex items-center justify-center text-2xl mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  {problem.icon}
                </div>

                {/* Title */}
                <h3 className="text-xl font-bold text-slate-900 mb-4 group-hover:text-slate-700 transition-colors">
                  {problem.title}
                </h3>

                {/* Description */}
                <p className="text-slate-600 leading-relaxed">
                  {problem.description}
                </p>

                {/* Hover Effect */}
                <div className={`absolute inset-0 bg-gradient-to-r ${problem.color} opacity-0 group-hover:opacity-5 rounded-2xl transition-opacity duration-300`} />
              </div>
            </motion.div>
          ))}
        </div>

        {/* Reception Desk Image */}
        <motion.div
          className="relative max-w-4xl mx-auto"
          initial={{ opacity: 0, scale: 0.9 }}
          animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.9 }}
          transition={{ duration: 0.8, delay: 0.8 }}
        >
          <div className="relative rounded-2xl overflow-hidden shadow-2xl">
            <Image
              src="/receptiondesk.jpg"
              alt="Overwhelmed reception desk"
              width={800}
              height={400}
              className="w-full h-64 md:h-80 object-cover"
            />
            
            {/* Overlay with Text */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent flex items-end justify-center p-8">
              <motion.div
                className="text-center text-white"
                initial={{ opacity: 0, y: 20 }}
                animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                transition={{ duration: 0.8, delay: 1.2 }}
              >
                <p className="text-lg font-semibold mb-2">
                  "We used to spend 4 hours daily just managing bookings..."
                </p>
                <p className="text-sm opacity-90">
                  - Tennis Club Manager (Before Tennis Booking Pro)
                </p>
              </motion.div>
            </div>

            {/* Floating Problem Indicators */}
            <motion.div
              className="absolute top-4 left-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-semibold"
              initial={{ opacity: 0, x: -20 }}
              animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -20 }}
              transition={{ duration: 0.6, delay: 1.4 }}
            >
              Double Bookings!
            </motion.div>
            
            <motion.div
              className="absolute top-4 right-4 bg-orange-500 text-white px-3 py-1 rounded-full text-sm font-semibold"
              initial={{ opacity: 0, x: 20 }}
              animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: 20 }}
              transition={{ duration: 0.6, delay: 1.6 }}
            >
              Lost Revenue
            </motion.div>
            
            <motion.div
              className="absolute bottom-20 left-1/2 transform -translate-x-1/2 bg-purple-500 text-white px-3 py-1 rounded-full text-sm font-semibold"
              initial={{ opacity: 0, y: 20 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
              transition={{ duration: 0.6, delay: 1.8 }}
            >
              Frustrated Members
            </motion.div>
          </div>
        </motion.div>

        {/* Transition to Solution */}
        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0 }}
          animate={isInView ? { opacity: 1 } : { opacity: 0 }}
          transition={{ duration: 0.8, delay: 2 }}
        >
          <motion.div
            className="inline-flex items-center text-blue-600 font-semibold text-lg"
            animate={{ y: [0, -5, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            There's a better way ↓
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}