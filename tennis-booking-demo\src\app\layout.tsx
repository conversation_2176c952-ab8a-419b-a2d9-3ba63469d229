import type { Metadata } from "next";
import { Inter, Inter_Tight } from "next/font/google";
import "./globals.css";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: "swap",
});

const interTight = Inter_Tight({
  variable: "--font-inter-tight",
  subsets: ["latin"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "Tennis Booking Pro - The Intelligent Tennis Club Platform",
  description: "Join 500+ forward-thinking clubs using AI-powered booking, social member engagement, and global payment solutions. 50% cheaper than competitors.",
  keywords: ["tennis booking", "court reservation", "club management", "AI-powered", "tennis platform"],
  authors: [{ name: "Tennis Booking Pro" }],
  viewport: "width=device-width, initial-scale=1",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <body
        className={`${inter.variable} ${interTight.variable} font-inter antialiased bg-white text-slate-900 overflow-x-hidden`}
      >
        {children}
      </body>
    </html>
  );
}
