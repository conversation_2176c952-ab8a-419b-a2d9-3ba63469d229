(()=>{var e={};e.id=436,e.ids=[436],e.modules={336:()=>{},440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var i=r(1658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,i.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1122:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},1135:()=>{},1322:(e,t)=>{"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:i,blurHeight:s,blurDataURL:n,objectFit:o}=e,a=i?40*i:t,l=s?40*s:r,d=a&&l?"viewBox='0 0 "+a+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+d+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(d?"none":"contain"===o?"xMidYMid":"cover"===o?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+n+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},2091:(e,t)=>{"use strict";function r(e){var t;let{config:r,src:i,width:s,quality:n}=e,o=n||(null==(t=r.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return r.path+"?url="+encodeURIComponent(i)+"&w="+s+"&q="+o+(i.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i}}),r.__next_img_default=!0;let i=r},2480:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return l},getImageProps:function(){return a}});let i=r(2639),s=r(9131),n=r(9603),o=i._(r(2091));function a(e){let{props:t}=(0,s.getImgProps)(e,{defaultLoader:o.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let l=n.Image},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3228:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,5814,23)),Promise.resolve().then(r.t.bind(r,6533,23))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3500:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4536,23)),Promise.resolve().then(r.t.bind(r,9603,23))},3873:e=>{"use strict";e.exports=require("path")},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>l});var i=r(7413),s=r(5230),n=r.n(s),o=r(7686),a=r.n(o);r(1135);let l={title:"Tennis Booking Pro - The Intelligent Tennis Club Platform",description:"Join 500+ forward-thinking clubs using AI-powered booking, social member engagement, and global payment solutions. 50% cheaper than competitors.",keywords:["tennis booking","court reservation","club management","AI-powered","tennis platform"],authors:[{name:"Tennis Booking Pro"}],viewport:"width=device-width, initial-scale=1"};function d({children:e}){return(0,i.jsx)("html",{lang:"en",className:"scroll-smooth",children:(0,i.jsx)("body",{className:`${n().variable} ${a().variable} font-inter antialiased bg-white text-slate-900 overflow-x-hidden`,children:e})})}},4536:(e,t,r)=>{let{createProxy:i}=r(9844);e.exports=i("C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\node_modules\\next\\dist\\client\\app-dir\\link.js")},5184:()=>{},6741:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var i=r(7413),s=r(4536),n=r.n(s),o=r(2480),a=r.n(o);function l(){return(0,i.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,i.jsx)("header",{className:"bg-white shadow-sm",children:(0,i.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)(n(),{href:"/",className:"flex items-center space-x-3",children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,i.jsx)("span",{className:"text-white font-bold text-lg",children:"\uD83C\uDFBE"})}),(0,i.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"Tennis Booking Pro"})]}),(0,i.jsx)(n(),{href:"/",className:"btn-primary",children:"Back to Home"})]})})}),(0,i.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16",children:[(0,i.jsxs)("div",{className:"text-center mb-16",children:[(0,i.jsx)("h1",{className:"heading-xl mb-6",children:"See Tennis Booking Pro in Action"}),(0,i.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Experience our platform with this interactive demo. See how easy it is to manage your tennis club with our professional booking system."})]}),(0,i.jsx)("div",{className:"max-w-4xl mx-auto mb-16",children:(0,i.jsx)("div",{className:"relative rounded-2xl overflow-hidden shadow-2xl bg-gradient-to-br from-blue-600 to-blue-800",children:(0,i.jsx)("div",{className:"aspect-video flex items-center justify-center",children:(0,i.jsxs)("div",{className:"text-center text-white",children:[(0,i.jsx)("div",{className:"w-24 h-24 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,i.jsx)("svg",{className:"w-10 h-10",fill:"currentColor",viewBox:"0 0 24 24",children:(0,i.jsx)("path",{d:"M8 5v14l11-7z"})})}),(0,i.jsx)("h3",{className:"text-2xl font-bold mb-2",children:"Interactive Demo"}),(0,i.jsx)("p",{className:"text-blue-100 mb-6",children:"See how Tennis Booking Pro transforms club management"}),(0,i.jsx)("button",{className:"bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors",children:"Start Demo"})]})})})}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 mb-16",children:[(0,i.jsxs)("div",{className:"feature-card text-center",children:[(0,i.jsx)("div",{className:"w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center text-blue-600 mx-auto mb-6",children:(0,i.jsx)("svg",{className:"w-8 h-8",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})})}),(0,i.jsx)("h3",{className:"heading-md mb-4",children:"Lightning Fast Booking"}),(0,i.jsx)("p",{className:"text-gray-600",children:"Watch how members can book courts in under 2 seconds with our intuitive interface."})]}),(0,i.jsxs)("div",{className:"feature-card text-center",children:[(0,i.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-2xl flex items-center justify-center text-green-600 mx-auto mb-6",children:(0,i.jsx)("svg",{className:"w-8 h-8",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})})}),(0,i.jsx)("h3",{className:"heading-md mb-4",children:"Powerful Analytics"}),(0,i.jsx)("p",{className:"text-gray-600",children:"Explore the comprehensive analytics dashboard that helps optimize your club's revenue."})]}),(0,i.jsxs)("div",{className:"feature-card text-center",children:[(0,i.jsx)("div",{className:"w-16 h-16 bg-orange-100 rounded-2xl flex items-center justify-center text-orange-600 mx-auto mb-6",children:(0,i.jsx)("svg",{className:"w-8 h-8",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 18h.01M8 21h8a1 1 0 001-1V4a1 1 0 00-1-1H8a1 1 0 00-1 1v16a1 1 0 001 1z"})})}),(0,i.jsx)("h3",{className:"heading-md mb-4",children:"Mobile Excellence"}),(0,i.jsx)("p",{className:"text-gray-600",children:"Experience the mobile-first design that works perfectly on any device."})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 mb-16",children:[(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)(a(),{src:"/lightcalender.jpg",alt:"Booking calendar interface",width:600,height:400,className:"rounded-xl shadow-lg w-full h-auto"}),(0,i.jsx)("div",{className:"absolute top-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg px-3 py-2",children:(0,i.jsx)("span",{className:"text-sm font-semibold text-gray-900",children:"Booking Calendar"})})]}),(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)(a(),{src:"/payment.jpg",alt:"Payment processing interface",width:600,height:400,className:"rounded-xl shadow-lg w-full h-auto"}),(0,i.jsx)("div",{className:"absolute top-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg px-3 py-2",children:(0,i.jsx)("span",{className:"text-sm font-semibold text-gray-900",children:"Payment Processing"})})]})]}),(0,i.jsxs)("div",{className:"text-center bg-white rounded-2xl p-12 shadow-lg",children:[(0,i.jsx)("h2",{className:"heading-lg mb-6",children:"Ready to Transform Your Tennis Club?"}),(0,i.jsx)("p",{className:"text-xl text-gray-600 mb-8 max-w-2xl mx-auto",children:"Join 500+ clubs already using Tennis Booking Pro to streamline operations and delight their members."}),(0,i.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,i.jsx)(n(),{href:"/signup",className:"btn-primary text-lg px-8 py-4",children:"Start Free Trial"}),(0,i.jsx)(n(),{href:"/contact",className:"btn-secondary text-lg px-8 py-4",children:"Schedule Personal Demo"})]})]})]})]})}},7111:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},7894:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return i}});let r=["default","imgix","cloudinary","akamai","custom"],i={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},7990:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var i=r(5239),s=r(8088),n=r(8170),o=r.n(n),a=r(893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let d={children:["",{children:["demo",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,6741)),"C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\app\\demo\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\app\\demo\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new i.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/demo/page",pathname:"/demo",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},8863:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9131:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return l}}),r(1122);let i=r(1322),s=r(7894),n=["-moz-initial","fill","none","scale-down",void 0];function o(e){return void 0!==e.default}function a(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function l(e,t){var r,l;let d,c,m,{src:u,sizes:h,unoptimized:p=!1,priority:g=!1,loading:x,className:f,quality:b,width:v,height:j,fill:w=!1,style:y,overrideSrc:P,onLoad:N,onLoadingComplete:k,placeholder:_="empty",blurDataURL:E,fetchPriority:C,decoding:S="async",layout:O,objectFit:D,objectPosition:z,lazyBoundary:M,lazyRoot:R,...B}=e,{imgConf:T,showAltText:I,blurComplete:A,defaultLoader:q}=t,L=T||s.imageConfigDefault;if("allSizes"in L)d=L;else{let e=[...L.deviceSizes,...L.imageSizes].sort((e,t)=>e-t),t=L.deviceSizes.sort((e,t)=>e-t),i=null==(r=L.qualities)?void 0:r.sort((e,t)=>e-t);d={...L,allSizes:e,deviceSizes:t,qualities:i}}if(void 0===q)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let G=B.loader||q;delete B.loader,delete B.srcSet;let F="__next_img_default"in G;if(F){if("custom"===d.loader)throw Object.defineProperty(Error('Image with src "'+u+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=G;G=t=>{let{config:r,...i}=t;return e(i)}}if(O){"fill"===O&&(w=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[O];e&&(y={...y,...e});let t={responsive:"100vw",fill:"100vw"}[O];t&&!h&&(h=t)}let W="",U=a(v),V=a(j);if((l=u)&&"object"==typeof l&&(o(l)||void 0!==l.src)){let e=o(u)?u.default:u;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(c=e.blurWidth,m=e.blurHeight,E=E||e.blurDataURL,W=e.src,!w)if(U||V){if(U&&!V){let t=U/e.width;V=Math.round(e.height*t)}else if(!U&&V){let t=V/e.height;U=Math.round(e.width*t)}}else U=e.width,V=e.height}let K=!g&&("lazy"===x||void 0===x);(!(u="string"==typeof u?u:W)||u.startsWith("data:")||u.startsWith("blob:"))&&(p=!0,K=!1),d.unoptimized&&(p=!0),F&&!d.dangerouslyAllowSVG&&u.split("?",1)[0].endsWith(".svg")&&(p=!0);let X=a(b),H=Object.assign(w?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:D,objectPosition:z}:{},I?{}:{color:"transparent"},y),J=A||"empty"===_?null:"blur"===_?'url("data:image/svg+xml;charset=utf-8,'+(0,i.getImageBlurSvg)({widthInt:U,heightInt:V,blurWidth:c,blurHeight:m,blurDataURL:E||"",objectFit:H.objectFit})+'")':'url("'+_+'")',Y=n.includes(H.objectFit)?"fill"===H.objectFit?"100% 100%":"cover":H.objectFit,$=J?{backgroundSize:Y,backgroundPosition:H.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:J}:{},Q=function(e){let{config:t,src:r,unoptimized:i,width:s,quality:n,sizes:o,loader:a}=e;if(i)return{src:r,srcSet:void 0,sizes:void 0};let{widths:l,kind:d}=function(e,t,r){let{deviceSizes:i,allSizes:s}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let i;i=e.exec(r);)t.push(parseInt(i[2]));if(t.length){let e=.01*Math.min(...t);return{widths:s.filter(t=>t>=i[0]*e),kind:"w"}}return{widths:s,kind:"w"}}return"number"!=typeof t?{widths:i,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>s.find(t=>t>=e)||s[s.length-1]))],kind:"x"}}(t,s,o),c=l.length-1;return{sizes:o||"w"!==d?o:"100vw",srcSet:l.map((e,i)=>a({config:t,src:r,quality:n,width:e})+" "+("w"===d?e:i+1)+d).join(", "),src:a({config:t,src:r,quality:n,width:l[c]})}}({config:d,src:u,unoptimized:p,width:U,quality:X,sizes:h,loader:G});return{props:{...B,loading:K?"lazy":x,fetchPriority:C,width:U,height:V,decoding:S,className:f,style:{...H,...$},sizes:Q.sizes,srcSet:Q.srcSet,src:P||Q.src},meta:{unoptimized:p,priority:g,placeholder:_,fill:w}}}},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9603:(e,t,r)=>{let{createProxy:i}=r(9844);e.exports=i("C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\node_modules\\next\\dist\\client\\image-component.js")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[447,787,567,533],()=>r(7990));module.exports=i})();