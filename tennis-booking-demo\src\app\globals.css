@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@400;500;600&display=swap');

:root {
  /* PREMIUM BRAND COLORS - ULTRA HIGH CONTRAST */
  --primary-blue: #0066ff;
  --primary-blue-dark: #0052cc;
  --primary-blue-light: #3385ff;
  --secondary-purple: #7c3aed;
  --accent-gradient: linear-gradient(135deg, #0066ff 0%, #7c3aed 50%, #ec4899 100%);
  --premium-gold: #fbbf24;
  --premium-silver: #e5e7eb;
  
  /* PREMIUM GRAYS */
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;
  
  /* PREMIUM SHADOWS */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --shadow-premium: 0 32px 64px -12px rgba(0, 102, 255, 0.25);
  
  /* GLASSMORPHISM */
  --glass-bg: rgba(255, 255, 255, 0.85);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  
  /* PREMIUM GRADIENTS */
  --gradient-hero: linear-gradient(135deg, #0066ff 0%, #7c3aed 50%, #ec4899 100%);
  --gradient-card: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  --gradient-text: linear-gradient(135deg, #0066ff 0%, #7c3aed 100%);
  --gradient-border: linear-gradient(135deg, #0066ff, #7c3aed, #ec4899);
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  overflow-x: hidden;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  line-height: 1.6;
  color: var(--gray-900);
  background: var(--gray-50);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* PREMIUM BUTTON SYSTEM */
.btn-primary {
  background: var(--gradient-hero);
  color: white;
  padding: 16px 32px;
  border-radius: 16px;
  font-weight: 600;
  font-size: 16px;
  text-decoration: none;
  display: inline-block;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  cursor: pointer;
  box-shadow: var(--shadow-lg);
  position: relative;
  overflow: hidden;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-premium);
}

.btn-primary:hover::before {
  opacity: 1;
}

.btn-secondary {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  color: var(--primary-blue);
  padding: 16px 32px;
  border-radius: 16px;
  font-weight: 600;
  font-size: 16px;
  text-decoration: none;
  display: inline-block;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid var(--glass-border);
  cursor: pointer;
  box-shadow: var(--glass-shadow);
}

.btn-secondary:hover {
  background: white;
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
  border-color: var(--primary-blue);
}

/* PREMIUM NAVIGATION */
.nav-link {
  color: var(--gray-600);
  text-decoration: none;
  font-weight: 500;
  font-size: 15px;
  padding: 12px 20px;
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.nav-link:hover {
  color: var(--primary-blue);
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  transform: translateY(-1px);
}

/* PREMIUM GLASSMORPHISM */
.glass-nav {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
}

.glass-card {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: 24px;
  box-shadow: var(--glass-shadow);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.glass-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-premium);
}

/* PREMIUM SECTION SPACING */
.section-padding {
  padding: 120px 0;
}

/* PREMIUM FEATURE CARDS */
.feature-card {
  background: var(--gradient-card);
  border: 1px solid var(--gray-200);
  border-radius: 24px;
  padding: 48px 40px;
  box-shadow: var(--shadow-md);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-hero);
  opacity: 0;
  transition: opacity 0.4s ease;
  z-index: 0;
}

.feature-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-premium);
  border-color: transparent;
}

.feature-card:hover::before {
  opacity: 0.03;
}

.feature-card > * {
  position: relative;
  z-index: 1;
}

/* PREMIUM PRICING CARDS */
.pricing-card {
  background: var(--gradient-card);
  border: 2px solid var(--gray-200);
  border-radius: 32px;
  padding: 56px 48px;
  position: relative;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.pricing-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-border);
  opacity: 0;
  z-index: 0;
  transition: opacity 0.4s ease;
}

.pricing-card::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  bottom: 2px;
  background: var(--gradient-card);
  border-radius: 30px;
  z-index: 1;
}

.pricing-card.featured {
  transform: scale(1.05);
  border: 2px solid transparent;
  background: var(--gradient-border);
}

.pricing-card.featured::before {
  opacity: 1;
}

.pricing-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-premium);
}

.pricing-card.featured:hover {
  transform: translateY(-8px) scale(1.07);
}

.pricing-card > * {
  position: relative;
  z-index: 2;
}

/* PREMIUM TYPOGRAPHY */
.heading-xl {
  font-size: clamp(3rem, 8vw, 5.5rem);
  font-weight: 900;
  line-height: 1.1;
  color: var(--gray-900);
  letter-spacing: -0.025em;
}

.heading-lg {
  font-size: clamp(2.5rem, 6vw, 4rem);
  font-weight: 800;
  line-height: 1.2;
  color: var(--gray-900);
  letter-spacing: -0.02em;
}

.heading-md {
  font-size: clamp(1.5rem, 4vw, 2.25rem);
  font-weight: 700;
  line-height: 1.3;
  color: var(--gray-900);
  letter-spacing: -0.015em;
}

.text-gradient {
  background: var(--gradient-text);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* PROFESSIONAL ANIMATIONS */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-8px); }
}

@keyframes glow {
  0%, 100% { box-shadow: var(--shadow-lg); }
  50% { box-shadow: var(--shadow-xl); }
}

@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slide-in-from-left {
  from { 
    opacity: 0; 
    transform: translateX(-30px);
  }
  to { 
    opacity: 1; 
    transform: translateX(0);
  }
}

@keyframes slide-in-from-right {
  from { 
    opacity: 0; 
    transform: translateX(30px);
  }
  to { 
    opacity: 1; 
    transform: translateX(0);
  }
}

@keyframes pulse-glow {
  0%, 100% { 
    box-shadow: 0 0 20px rgba(0, 102, 255, 0.3);
  }
  50% { 
    box-shadow: 0 0 40px rgba(0, 102, 255, 0.6);
  }
}

.animate-float {
  animation: float 8s ease-in-out infinite;
}

.animate-glow {
  animation: glow 3s ease-in-out infinite;
}

.animate-in {
  animation-fill-mode: both;
}

.fade-in {
  animation: fade-in 0.6s ease-out;
}

.slide-in-from-left {
  animation: slide-in-from-left 0.6s ease-out;
}

.slide-in-from-right {
  animation: slide-in-from-right 0.6s ease-out;
}

.duration-200 { animation-duration: 200ms; }
.duration-300 { animation-duration: 300ms; }
.duration-500 { animation-duration: 500ms; }
.duration-1000 { animation-duration: 1000ms; }

.delay-200 { animation-delay: 200ms; }
.delay-300 { animation-delay: 300ms; }
.delay-400 { animation-delay: 400ms; }
.delay-600 { animation-delay: 600ms; }
.delay-800 { animation-delay: 800ms; }
.delay-1000 { animation-delay: 1000ms; }
.delay-1200 { animation-delay: 1200ms; }
.delay-1400 { animation-delay: 1400ms; }

/* RESPONSIVE BREAKPOINTS */
@media (max-width: 768px) {
  .section-padding {
    padding: 80px 0;
  }
  
  .feature-card {
    padding: 32px 24px;
  }
  
  .pricing-card {
    padding: 40px 32px;
  }
}
