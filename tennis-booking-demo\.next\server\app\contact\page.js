(()=>{var e={};e.id=977,e.ids=[977],e.modules={336:()=>{},440:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>n});var t=r(1658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1581:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>o});var t=r(687),n=r(3210),a=r(5814),i=r.n(a);function o(){let[e,s]=(0,n.useState)({name:"",email:"",club:"",message:""}),r=e=>{s(s=>({...s,[e.target.name]:e.target.value}))};return(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)("header",{className:"glass-nav sticky top-0 z-50",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,t.jsxs)(i(),{href:"/",className:"flex items-center space-x-3 hover:transform hover:scale-105 transition-all duration-300",children:[(0,t.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl flex items-center justify-center shadow-lg",children:(0,t.jsx)("span",{className:"text-white font-bold text-lg",children:"\uD83C\uDFBE"})}),(0,t.jsx)("span",{className:"text-xl font-bold text-gradient",children:"Tennis Booking Pro"})]})})}),(0,t.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16",children:[(0,t.jsxs)("div",{className:"text-center mb-16",children:[(0,t.jsx)("h1",{className:"heading-xl mb-6",children:"Get in Touch"}),(0,t.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Have questions about Tennis Booking Pro? We're here to help you transform your tennis club management."})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,t.jsx)("div",{className:"lg:col-span-1",children:(0,t.jsxs)("div",{className:"glass-card p-8",children:[(0,t.jsx)("h2",{className:"heading-md mb-6",children:"Contact Information"}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-100 to-blue-50 rounded-xl flex items-center justify-center text-blue-600 mr-4 shadow-md",children:(0,t.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-900",children:"Email"}),(0,t.jsx)("p",{className:"text-gray-600",children:"<EMAIL>"})]})]}),(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-green-100 to-green-50 rounded-xl flex items-center justify-center text-green-600 mr-4 shadow-md",children:(0,t.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"})})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-900",children:"Phone"}),(0,t.jsx)("p",{className:"text-gray-600",children:"+****************"})]})]}),(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-orange-100 to-orange-50 rounded-xl flex items-center justify-center text-orange-600 mr-4 shadow-md",children:(0,t.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-900",children:"Response Time"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Within 24 hours"})]})]})]}),(0,t.jsxs)("div",{className:"mt-8 pt-8 border-t border-gray-200",children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-900 mb-4",children:"Quick Links"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(i(),{href:"/demo",className:"nav-link inline-block",children:"Schedule a Demo"}),(0,t.jsx)(i(),{href:"/signup",className:"nav-link inline-block",children:"Start Free Trial"}),(0,t.jsx)(i(),{href:"/help",className:"nav-link inline-block",children:"Help Center"})]})]})]})}),(0,t.jsx)("div",{className:"lg:col-span-2",children:(0,t.jsxs)("div",{className:"glass-card p-8",children:[(0,t.jsx)("h2",{className:"heading-md mb-6",children:"Send us a Message"}),(0,t.jsxs)("form",{onSubmit:e=>{e.preventDefault(),alert("Contact form submission will be implemented with backend integration.")},className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:"Your Name"}),(0,t.jsx)("input",{type:"text",id:"name",name:"name",required:!0,value:e.name,onChange:r,className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white/70 backdrop-blur-sm transition-all duration-300 hover:bg-white focus:bg-white",placeholder:"John Doe"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),(0,t.jsx)("input",{type:"email",id:"email",name:"email",required:!0,value:e.email,onChange:r,className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white/70 backdrop-blur-sm transition-all duration-300 hover:bg-white focus:bg-white",placeholder:"<EMAIL>"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"club",className:"block text-sm font-medium text-gray-700 mb-2",children:"Tennis Club Name"}),(0,t.jsx)("input",{type:"text",id:"club",name:"club",value:e.club,onChange:r,className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white/70 backdrop-blur-sm transition-all duration-300 hover:bg-white focus:bg-white",placeholder:"Berlin Tennis Club (optional)"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 mb-2",children:"Message"}),(0,t.jsx)("textarea",{id:"message",name:"message",rows:5,required:!0,value:e.message,onChange:r,className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white/70 backdrop-blur-sm transition-all duration-300 hover:bg-white focus:bg-white",placeholder:"Tell us about your tennis club and how we can help..."})]}),(0,t.jsx)("button",{type:"submit",className:"w-full btn-primary text-lg py-3",children:"Send Message"})]})]})})]})]})]})}},2549:(e,s,r)=>{Promise.resolve().then(r.bind(r,1581))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3839:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\app\\\\contact\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\app\\contact\\page.tsx","default")},3873:e=>{"use strict";e.exports=require("path")},4428:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>h,tree:()=>d});var t=r(5239),n=r(8088),a=r(8170),i=r.n(a),o=r(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(s,l);let d={children:["",{children:["contact",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,3839)),"C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\app\\contact\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\app\\contact\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/contact/page",pathname:"/contact",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},4431:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>d,metadata:()=>l});var t=r(7413),n=r(5230),a=r.n(n),i=r(7686),o=r.n(i);r(1135);let l={title:"Tennis Booking Pro - The Intelligent Tennis Club Platform",description:"Join 500+ forward-thinking clubs using AI-powered booking, social member engagement, and global payment solutions. 50% cheaper than competitors.",keywords:["tennis booking","court reservation","club management","AI-powered","tennis platform"],authors:[{name:"Tennis Booking Pro"}],viewport:"width=device-width, initial-scale=1"};function d({children:e}){return(0,t.jsx)("html",{lang:"en",className:"scroll-smooth",children:(0,t.jsx)("body",{className:`${a().variable} ${o().variable} font-inter antialiased bg-white text-slate-900 overflow-x-hidden`,children:e})})}},5184:()=>{},7111:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},8863:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9397:(e,s,r)=>{Promise.resolve().then(r.bind(r,3839))},9551:e=>{"use strict";e.exports=require("url")}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[447,787,567],()=>r(4428));module.exports=t})();