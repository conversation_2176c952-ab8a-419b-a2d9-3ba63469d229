'use client'

import { useState } from 'react'
import Link from 'next/link'

const languages = [
  { code: 'en', name: 'English', flag: '🇺🇸' },
  { code: 'de', name: '<PERSON><PERSON><PERSON>', flag: '🇩🇪' },
  { code: 'fr', name: 'Français', flag: '🇫🇷' },
  { code: 'es', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', flag: '🇪🇸' },
  { code: 'it', name: 'Italiano', flag: '🇮🇹' },
  { code: 'nl', name: 'Nederlands', flag: '🇳🇱' },
]

interface NavigationProps {
  currentLanguage?: string
  onLanguageChange?: (lang: string) => void
}

export function Navigation({ currentLanguage = 'en', onLanguageChange }: NavigationProps) {
  const [showLangDropdown, setShowLangDropdown] = useState(false)

  const currentLang = languages.find(lang => lang.code === currentLanguage) || languages[0]

  return (
    <nav className="glass-nav sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-20">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-3 hover:transform hover:scale-105 transition-all duration-300 group">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300">
                <span className="text-white font-bold text-lg">🎾</span>
              </div>
              <span className="text-xl font-bold text-gradient">
                Tennis Booking Pro
              </span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-2">
            <a href="#features" className="nav-link">
              Features
            </a>
            <a href="#pricing" className="nav-link">
              Pricing
            </a>
            <Link href="/demo" className="nav-link">
              Demo
            </Link>
            <Link href="/about" className="nav-link">
              About
            </Link>
            <Link href="/contact" className="nav-link">
              Contact
            </Link>
          </div>

          {/* Right Side */}
          <div className="flex items-center space-x-4">
            {/* Language Switcher */}
            <div className="relative">
              <button
                onClick={() => setShowLangDropdown(!showLangDropdown)}
                className="nav-link flex items-center space-x-2"
              >
                <span>{currentLang.flag}</span>
                <span>{currentLang.code.toUpperCase()}</span>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              {showLangDropdown && (
                <div className="absolute right-0 mt-2 w-52 glass-card p-2 animate-in fade-in duration-200">
                  {languages.map((lang) => (
                    <button
                      key={lang.code}
                      onClick={() => {
                        onLanguageChange?.(lang.code)
                        setShowLangDropdown(false)
                      }}
                      className="w-full text-left px-4 py-3 text-sm text-gray-700 hover:bg-white/50 rounded-lg flex items-center space-x-3 transition-all duration-200 hover:transform hover:scale-[1.02]"
                    >
                      <span>{lang.flag}</span>
                      <span>{lang.name}</span>
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Login */}
            <Link href="/login" className="nav-link">
              Login
            </Link>

            {/* CTA Button */}
            <Link href="/signup" className="btn-primary">
              Start Free Trial
            </Link>
          </div>
        </div>
      </div>
    </nav>
  )
}