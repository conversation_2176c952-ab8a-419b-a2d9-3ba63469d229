(()=>{var e={};e.id=877,e.ids=[877],e.modules={336:()=>{},440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(1658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1965:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4536,23))},2285:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,5814,23))},2856:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(5239),n=r(8088),i=r(8170),a=r.n(i),o=r(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["privacy",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,9255)),"C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\app\\privacy\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\app\\privacy\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/privacy/page",pathname:"/privacy",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>l});var s=r(7413),n=r(5230),i=r.n(n),a=r(7686),o=r.n(a);r(1135);let l={title:"Tennis Booking Pro - The Intelligent Tennis Club Platform",description:"Join 500+ forward-thinking clubs using AI-powered booking, social member engagement, and global payment solutions. 50% cheaper than competitors.",keywords:["tennis booking","court reservation","club management","AI-powered","tennis platform"],authors:[{name:"Tennis Booking Pro"}],viewport:"width=device-width, initial-scale=1"};function d({children:e}){return(0,s.jsx)("html",{lang:"en",className:"scroll-smooth",children:(0,s.jsx)("body",{className:`${i().variable} ${o().variable} font-inter antialiased bg-white text-slate-900 overflow-x-hidden`,children:e})})}},4536:(e,t,r)=>{let{createProxy:s}=r(9844);e.exports=s("C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\node_modules\\next\\dist\\client\\app-dir\\link.js")},5184:()=>{},7111:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},8863:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9255:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(7413),n=r(4536),i=r.n(n);function a(){return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsx)("header",{className:"glass-nav sticky top-0 z-50",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,s.jsxs)(i(),{href:"/",className:"flex items-center space-x-3 hover:transform hover:scale-105 transition-all duration-300",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl flex items-center justify-center shadow-lg",children:(0,s.jsx)("span",{className:"text-white font-bold text-lg",children:"\uD83C\uDFBE"})}),(0,s.jsx)("span",{className:"text-xl font-bold text-gradient",children:"Tennis Booking Pro"})]})})}),(0,s.jsx)("main",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16",children:(0,s.jsxs)("div",{className:"glass-card p-12",children:[(0,s.jsx)("h1",{className:"heading-xl mb-8",children:"Privacy Policy"}),(0,s.jsx)("p",{className:"text-gray-600 mb-8",children:"Last updated: January 2025"}),(0,s.jsxs)("div",{className:"prose prose-lg max-w-none space-y-8",children:[(0,s.jsxs)("section",{children:[(0,s.jsx)("h2",{className:"heading-md mb-4 text-gradient",children:"Information We Collect"}),(0,s.jsx)("p",{className:"text-gray-600",children:"We collect information you provide directly to us, such as when you create an account, make bookings, or contact us for support. This includes your name, email address, and club information."})]}),(0,s.jsxs)("section",{children:[(0,s.jsx)("h2",{className:"heading-md mb-4 text-gradient",children:"How We Use Your Information"}),(0,s.jsx)("p",{className:"text-gray-600",children:"We use the information we collect to provide, maintain, and improve our services, process transactions, send you technical notices and support messages, and communicate with you about products and services."})]}),(0,s.jsxs)("section",{children:[(0,s.jsx)("h2",{className:"heading-md mb-4 text-gradient",children:"Data Security"}),(0,s.jsx)("p",{className:"text-gray-600",children:"We implement appropriate technical and organizational measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction. All data is encrypted in transit and at rest."})]}),(0,s.jsxs)("section",{children:[(0,s.jsx)("h2",{className:"heading-md mb-4 text-gradient",children:"GDPR Compliance"}),(0,s.jsx)("p",{className:"text-gray-600",children:"We are fully GDPR compliant and respect your rights under European data protection law. You have the right to access, update, or delete your personal information at any time."})]}),(0,s.jsxs)("section",{children:[(0,s.jsx)("h2",{className:"heading-md mb-4 text-gradient",children:"Contact Us"}),(0,s.jsxs)("p",{className:"text-gray-600",children:["If you have any questions about this Privacy Policy, please contact us at"," ",(0,s.jsx)("a",{href:"mailto:<EMAIL>",className:"text-blue-600 hover:text-blue-500",children:"<EMAIL>"})]})]})]})]})})]})}},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,787,567],()=>r(2856));module.exports=s})();