(()=>{var e={};e.id=220,e.ids=[220],e.modules={336:()=>{},440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(1658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1820:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>a.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>h,tree:()=>d});var r=s(5239),n=s(8088),i=s(8170),a=s.n(i),o=s(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let d={children:["",{children:["about",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,8770)),"C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\app\\about\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\app\\about\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/about/page",pathname:"/about",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},1965:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,4536,23))},2285:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,5814,23))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d,metadata:()=>l});var r=s(7413),n=s(5230),i=s.n(n),a=s(7686),o=s.n(a);s(1135);let l={title:"Tennis Booking Pro - The Intelligent Tennis Club Platform",description:"Join 500+ forward-thinking clubs using AI-powered booking, social member engagement, and global payment solutions. 50% cheaper than competitors.",keywords:["tennis booking","court reservation","club management","AI-powered","tennis platform"],authors:[{name:"Tennis Booking Pro"}],viewport:"width=device-width, initial-scale=1"};function d({children:e}){return(0,r.jsx)("html",{lang:"en",className:"scroll-smooth",children:(0,r.jsx)("body",{className:`${i().variable} ${o().variable} font-inter antialiased bg-white text-slate-900 overflow-x-hidden`,children:e})})}},4536:(e,t,s)=>{let{createProxy:r}=s(9844);e.exports=r("C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\node_modules\\next\\dist\\client\\app-dir\\link.js")},5184:()=>{},7111:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6346,23)),Promise.resolve().then(s.t.bind(s,7924,23)),Promise.resolve().then(s.t.bind(s,5656,23)),Promise.resolve().then(s.t.bind(s,99,23)),Promise.resolve().then(s.t.bind(s,8243,23)),Promise.resolve().then(s.t.bind(s,8827,23)),Promise.resolve().then(s.t.bind(s,2763,23)),Promise.resolve().then(s.t.bind(s,7173,23))},8770:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(7413),n=s(4536),i=s.n(n);function a(){return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("header",{className:"glass-nav sticky top-0 z-50",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,r.jsxs)(i(),{href:"/",className:"flex items-center space-x-3 hover:transform hover:scale-105 transition-all duration-300",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl flex items-center justify-center shadow-lg",children:(0,r.jsx)("span",{className:"text-white font-bold text-lg",children:"\uD83C\uDFBE"})}),(0,r.jsx)("span",{className:"text-xl font-bold text-gradient",children:"Tennis Booking Pro"})]})})}),(0,r.jsx)("main",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16",children:(0,r.jsxs)("div",{className:"glass-card p-12",children:[(0,r.jsx)("h1",{className:"heading-xl mb-8",children:"About Tennis Booking Pro"}),(0,r.jsxs)("div",{className:"prose prose-lg max-w-none",children:[(0,r.jsx)("p",{className:"text-xl text-gray-600 mb-8",children:"We're on a mission to modernize tennis club management worldwide. Our platform helps clubs save time, increase revenue, and create better experiences for their members."}),(0,r.jsx)("h2",{className:"heading-md mt-12 mb-6 text-gradient",children:"Our Story"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"Founded by tennis enthusiasts who experienced the frustration of outdated booking systems firsthand, Tennis Booking Pro was created to solve the real problems tennis clubs face every day."}),(0,r.jsx)("h2",{className:"heading-md mt-12 mb-6 text-gradient",children:"Why We're Different"}),(0,r.jsxs)("ul",{className:"space-y-4 text-gray-600",children:[(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"text-blue-600 mr-2",children:"•"}),(0,r.jsx)("strong",{children:"Tennis-focused:"})," Built specifically for tennis clubs, not adapted from generic booking software"]}),(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"text-blue-600 mr-2",children:"•"}),(0,r.jsx)("strong",{children:"Affordable:"})," 50% cheaper than competitors without compromising on features"]}),(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"text-blue-600 mr-2",children:"•"}),(0,r.jsx)("strong",{children:"Global reach:"})," Multi-language support and international payment methods"]}),(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"text-blue-600 mr-2",children:"•"}),(0,r.jsx)("strong",{children:"Modern technology:"})," Built with the latest web technologies for speed and reliability"]})]}),(0,r.jsx)("h2",{className:"heading-md mt-12 mb-6 text-gradient",children:"Join 500+ Clubs Worldwide"}),(0,r.jsx)("p",{className:"text-gray-600 mb-8",children:"From small local clubs to international tennis academies, Tennis Booking Pro is trusted by clubs around the world to manage their operations efficiently."}),(0,r.jsxs)("div",{className:"glass-card p-8 text-center mt-12",children:[(0,r.jsx)("h3",{className:"heading-md mb-4 text-gradient",children:"Ready to modernize your club?"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"Join the tennis clubs that have already transformed their operations."}),(0,r.jsx)(i(),{href:"/signup",className:"btn-primary",children:"Start Free Trial"})]})]})]})})]})}},8863:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6444,23)),Promise.resolve().then(s.t.bind(s,6042,23)),Promise.resolve().then(s.t.bind(s,8170,23)),Promise.resolve().then(s.t.bind(s,9477,23)),Promise.resolve().then(s.t.bind(s,9345,23)),Promise.resolve().then(s.t.bind(s,2089,23)),Promise.resolve().then(s.t.bind(s,6577,23)),Promise.resolve().then(s.t.bind(s,1307,23))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,787,567],()=>s(1820));module.exports=r})();