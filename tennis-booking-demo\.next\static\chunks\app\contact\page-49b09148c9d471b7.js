(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[977],{6243:(e,s,a)=>{Promise.resolve().then(a.bind(a,8983))},8983:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});var l=a(5155),r=a(2115),n=a(6874),i=a.n(n);function t(){let[e,s]=(0,r.useState)({name:"",email:"",club:"",message:""}),a=e=>{s(s=>({...s,[e.target.name]:e.target.value}))};return(0,l.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,l.jsx)("header",{className:"glass-nav sticky top-0 z-50",children:(0,l.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,l.jsxs)(i(),{href:"/",className:"flex items-center space-x-3 hover:transform hover:scale-105 transition-all duration-300",children:[(0,l.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl flex items-center justify-center shadow-lg",children:(0,l.jsx)("span",{className:"text-white font-bold text-lg",children:"\uD83C\uDFBE"})}),(0,l.jsx)("span",{className:"text-xl font-bold text-gradient",children:"Tennis Booking Pro"})]})})}),(0,l.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16",children:[(0,l.jsxs)("div",{className:"text-center mb-16",children:[(0,l.jsx)("h1",{className:"heading-xl mb-6",children:"Get in Touch"}),(0,l.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Have questions about Tennis Booking Pro? We're here to help you transform your tennis club management."})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,l.jsx)("div",{className:"lg:col-span-1",children:(0,l.jsxs)("div",{className:"glass-card p-8",children:[(0,l.jsx)("h2",{className:"heading-md mb-6",children:"Contact Information"}),(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"flex items-start",children:[(0,l.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-100 to-blue-50 rounded-xl flex items-center justify-center text-blue-600 mr-4 shadow-md",children:(0,l.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,l.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"font-semibold text-gray-900",children:"Email"}),(0,l.jsx)("p",{className:"text-gray-600",children:"<EMAIL>"})]})]}),(0,l.jsxs)("div",{className:"flex items-start",children:[(0,l.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-green-100 to-green-50 rounded-xl flex items-center justify-center text-green-600 mr-4 shadow-md",children:(0,l.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,l.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"})})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"font-semibold text-gray-900",children:"Phone"}),(0,l.jsx)("p",{className:"text-gray-600",children:"+****************"})]})]}),(0,l.jsxs)("div",{className:"flex items-start",children:[(0,l.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-orange-100 to-orange-50 rounded-xl flex items-center justify-center text-orange-600 mr-4 shadow-md",children:(0,l.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,l.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"font-semibold text-gray-900",children:"Response Time"}),(0,l.jsx)("p",{className:"text-gray-600",children:"Within 24 hours"})]})]})]}),(0,l.jsxs)("div",{className:"mt-8 pt-8 border-t border-gray-200",children:[(0,l.jsx)("h3",{className:"font-semibold text-gray-900 mb-4",children:"Quick Links"}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(i(),{href:"/demo",className:"nav-link inline-block",children:"Schedule a Demo"}),(0,l.jsx)(i(),{href:"/signup",className:"nav-link inline-block",children:"Start Free Trial"}),(0,l.jsx)(i(),{href:"/help",className:"nav-link inline-block",children:"Help Center"})]})]})]})}),(0,l.jsx)("div",{className:"lg:col-span-2",children:(0,l.jsxs)("div",{className:"glass-card p-8",children:[(0,l.jsx)("h2",{className:"heading-md mb-6",children:"Send us a Message"}),(0,l.jsxs)("form",{onSubmit:e=>{e.preventDefault(),alert("Contact form submission will be implemented with backend integration.")},className:"space-y-6",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:"Your Name"}),(0,l.jsx)("input",{type:"text",id:"name",name:"name",required:!0,value:e.name,onChange:a,className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white/70 backdrop-blur-sm transition-all duration-300 hover:bg-white focus:bg-white",placeholder:"John Doe"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),(0,l.jsx)("input",{type:"email",id:"email",name:"email",required:!0,value:e.email,onChange:a,className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white/70 backdrop-blur-sm transition-all duration-300 hover:bg-white focus:bg-white",placeholder:"<EMAIL>"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"club",className:"block text-sm font-medium text-gray-700 mb-2",children:"Tennis Club Name"}),(0,l.jsx)("input",{type:"text",id:"club",name:"club",value:e.club,onChange:a,className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white/70 backdrop-blur-sm transition-all duration-300 hover:bg-white focus:bg-white",placeholder:"Berlin Tennis Club (optional)"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 mb-2",children:"Message"}),(0,l.jsx)("textarea",{id:"message",name:"message",rows:5,required:!0,value:e.message,onChange:a,className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white/70 backdrop-blur-sm transition-all duration-300 hover:bg-white focus:bg-white",placeholder:"Tell us about your tennis club and how we can help..."})]}),(0,l.jsx)("button",{type:"submit",className:"w-full btn-primary text-lg py-3",children:"Send Message"})]})]})})]})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[874,441,684,358],()=>s(6243)),_N_E=e.O()}]);