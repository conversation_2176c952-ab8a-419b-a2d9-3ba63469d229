import Link from 'next/link'
import Image from 'next/image'

export default function DemoPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">🎾</span>
              </div>
              <span className="text-xl font-bold text-gray-900">
                Tennis Booking Pro
              </span>
            </Link>
            <Link href="/" className="btn-primary">
              Back to Home
            </Link>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-16">
          <h1 className="heading-xl mb-6">
            See Tennis Booking Pro in Action
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Experience our platform with this interactive demo. See how easy it is to manage 
            your tennis club with our professional booking system.
          </p>
        </div>

        {/* Demo Video/Image Placeholder */}
        <div className="max-w-4xl mx-auto mb-16">
          <div className="relative rounded-2xl overflow-hidden shadow-2xl bg-gradient-to-br from-blue-600 to-blue-800">
            <div className="aspect-video flex items-center justify-center">
              <div className="text-center text-white">
                <div className="w-24 h-24 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-10 h-10" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8 5v14l11-7z"/>
                  </svg>
                </div>
                <h3 className="text-2xl font-bold mb-2">Interactive Demo</h3>
                <p className="text-blue-100 mb-6">
                  See how Tennis Booking Pro transforms club management
                </p>
                <button className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors">
                  Start Demo
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Feature Highlights */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          <div className="feature-card text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center text-blue-600 mx-auto mb-6">
              <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <h3 className="heading-md mb-4">Lightning Fast Booking</h3>
            <p className="text-gray-600">
              Watch how members can book courts in under 2 seconds with our intuitive interface.
            </p>
          </div>

          <div className="feature-card text-center">
            <div className="w-16 h-16 bg-green-100 rounded-2xl flex items-center justify-center text-green-600 mx-auto mb-6">
              <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <h3 className="heading-md mb-4">Powerful Analytics</h3>
            <p className="text-gray-600">
              Explore the comprehensive analytics dashboard that helps optimize your club's revenue.
            </p>
          </div>

          <div className="feature-card text-center">
            <div className="w-16 h-16 bg-orange-100 rounded-2xl flex items-center justify-center text-orange-600 mx-auto mb-6">
              <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a1 1 0 001-1V4a1 1 0 00-1-1H8a1 1 0 00-1 1v16a1 1 0 001 1z" />
              </svg>
            </div>
            <h3 className="heading-md mb-4">Mobile Excellence</h3>
            <p className="text-gray-600">
              Experience the mobile-first design that works perfectly on any device.
            </p>
          </div>
        </div>

        {/* Demo Screenshots */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
          <div className="relative">
            <Image
              src="/lightcalender.jpg"
              alt="Booking calendar interface"
              width={600}
              height={400}
              className="rounded-xl shadow-lg w-full h-auto"
            />
            <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg px-3 py-2">
              <span className="text-sm font-semibold text-gray-900">Booking Calendar</span>
            </div>
          </div>

          <div className="relative">
            <Image
              src="/payment.jpg"
              alt="Payment processing interface"
              width={600}
              height={400}
              className="rounded-xl shadow-lg w-full h-auto"
            />
            <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg px-3 py-2">
              <span className="text-sm font-semibold text-gray-900">Payment Processing</span>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center bg-white rounded-2xl p-12 shadow-lg">
          <h2 className="heading-lg mb-6">Ready to Transform Your Tennis Club?</h2>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Join 500+ clubs already using Tennis Booking Pro to streamline operations 
            and delight their members.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/signup" className="btn-primary text-lg px-8 py-4">
              Start Free Trial
            </Link>
            <Link href="/contact" className="btn-secondary text-lg px-8 py-4">
              Schedule Personal Demo
            </Link>
          </div>
        </div>
      </main>
    </div>
  )
}