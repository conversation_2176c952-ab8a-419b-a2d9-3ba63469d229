(()=>{var e={};e.id=520,e.ids=[520],e.modules={336:()=>{},440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(1658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3686:(e,t,r)=>{Promise.resolve().then(r.bind(r,4934))},3873:e=>{"use strict";e.exports=require("path")},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>l});var s=r(7413),n=r(5230),o=r.n(n),i=r(7686),a=r.n(i);r(1135);let l={title:"Tennis Booking Pro - The Intelligent Tennis Club Platform",description:"Join 500+ forward-thinking clubs using AI-powered booking, social member engagement, and global payment solutions. 50% cheaper than competitors.",keywords:["tennis booking","court reservation","club management","AI-powered","tennis platform"],authors:[{name:"Tennis Booking Pro"}],viewport:"width=device-width, initial-scale=1"};function d({children:e}){return(0,s.jsx)("html",{lang:"en",className:"scroll-smooth",children:(0,s.jsx)("body",{className:`${o().variable} ${a().variable} font-inter antialiased bg-white text-slate-900 overflow-x-hidden`,children:e})})}},4934:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\app\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\app\\login\\page.tsx","default")},5184:()=>{},7052:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=r(5239),n=r(8088),o=r(8170),i=r.n(o),a=r(893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let d={children:["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,4934)),"C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\app\\login\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\app\\login\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},7111:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},8190:(e,t,r)=>{Promise.resolve().then(r.bind(r,9488))},8863:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9488:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(687),n=r(3210),o=r(5814),i=r.n(o);function a(){let[e,t]=(0,n.useState)(""),[r,o]=(0,n.useState)(""),[a,l]=(0,n.useState)(!1),d=async e=>{e.preventDefault(),l(!0),setTimeout(()=>{l(!1),alert("Login functionality will be implemented with Supabase authentication.")},1e3)};return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsxs)(i(),{href:"/",className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-white font-bold text-xl",children:"\uD83C\uDFBE"})}),(0,s.jsx)("span",{className:"text-2xl font-bold text-gray-900",children:"Tennis Booking Pro"})]})}),(0,s.jsx)("h2",{className:"mt-6 text-center text-3xl font-bold text-gray-900",children:"Sign in to your account"}),(0,s.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Or"," ",(0,s.jsx)(i(),{href:"/signup",className:"font-medium text-blue-600 hover:text-blue-500",children:"start your free trial"})]})]}),(0,s.jsx)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:(0,s.jsxs)("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[(0,s.jsxs)("form",{className:"space-y-6",onSubmit:d,children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),(0,s.jsx)("div",{className:"mt-1",children:(0,s.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e,onChange:e=>t(e.target.value),className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter your email"})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,s.jsx)("div",{className:"mt-1",children:(0,s.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,value:r,onChange:e=>o(e.target.value),className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter your password"})})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,s.jsx)("label",{htmlFor:"remember-me",className:"ml-2 block text-sm text-gray-900",children:"Remember me"})]}),(0,s.jsx)("div",{className:"text-sm",children:(0,s.jsx)("a",{href:"#",className:"font-medium text-blue-600 hover:text-blue-500",children:"Forgot your password?"})})]}),(0,s.jsx)("div",{children:(0,s.jsx)("button",{type:"submit",disabled:a,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:a?"Signing in...":"Sign in"})})]}),(0,s.jsxs)("div",{className:"mt-6",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,s.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,s.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,s.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"Or continue with"})})]}),(0,s.jsxs)("div",{className:"mt-6 grid grid-cols-2 gap-3",children:[(0,s.jsxs)("button",{className:"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50",children:[(0,s.jsxs)("svg",{className:"h-5 w-5",viewBox:"0 0 24 24",children:[(0,s.jsx)("path",{fill:"currentColor",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,s.jsx)("path",{fill:"currentColor",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,s.jsx)("path",{fill:"currentColor",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,s.jsx)("path",{fill:"currentColor",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),(0,s.jsx)("span",{className:"ml-2",children:"Google"})]}),(0,s.jsxs)("button",{className:"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50",children:[(0,s.jsx)("svg",{className:"h-5 w-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{d:"M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.024-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.098.119.112.223.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.747 2.848c-.269 1.045-1.004 2.352-1.498 3.146 1.123.345 2.306.535 3.55.535 6.624 0 11.99-5.367 11.99-11.99C24.007 5.367 18.641.001 12.017.001z"})}),(0,s.jsx)("span",{className:"ml-2",children:"Microsoft"})]})]})]})]})})]})}},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,787,567],()=>r(7052));module.exports=s})();