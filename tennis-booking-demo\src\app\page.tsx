'use client'

import { useState } from 'react'
import { Navigation } from '@/components/layout/Navigation'
import { Hero } from '@/components/landing/Hero'
import { Features } from '@/components/landing/Features'
import { Pricing } from '@/components/landing/Pricing'
import { Footer } from '@/components/layout/Footer'
import { translations } from '@/lib/translations'

export default function Home() {
  const [currentLanguage, setCurrentLanguage] = useState('en')
  
  const t = translations[currentLanguage as keyof typeof translations] || translations.en

  return (
    <main className="min-h-screen">
      <Navigation 
        currentLanguage={currentLanguage}
        onLanguageChange={setCurrentLanguage}
      />
      <Hero translations={t.hero} />
      <Features translations={t.features} />
      <Pricing translations={t.pricing} />
      <Footer translations={t.footer} />
    </main>
  )
}
